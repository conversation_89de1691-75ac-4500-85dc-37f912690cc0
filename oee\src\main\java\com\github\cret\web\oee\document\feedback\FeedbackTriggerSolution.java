package com.github.cret.web.oee.document.feedback;

import java.util.Date;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document("t_feedback_trigger_solution")
// 反馈触发解决方案类，记录解决方案内容、解决人信息及解决时间
public class FeedbackTriggerSolution {

	@Id
	private String id;

	// 解决方案内容
	@Field(name = "solution")
	private String solution;

	// 解决人ID
	@Field(name = "solver_id")
	private String solverId;

	// 解决人姓名
	@Field(name = "solver_name")
	private String solverName;

	// 解决时间
	@Field(name = "solve_time")
	private Date solveTime;

	// 触发记录ID
	@Field(name = "trigger_record_id")
	private String triggerRecordId;

	// 发送记录ID
	@Field(name = "trigger_send_id")
	private String triggerSendId;

	@Field(name = "create_time")
	private Date createTime;

	@Field(name = "update_time")
	private Date updateTime;

	@Field(name = "create_by")
	private String createBy;

	@Field(name = "update_by")
	private String updateBy;

	// 是否提交
	@Field(name = "submitted")
	private Boolean submitted;

	// 提交时间
	@Field(name = "submit_time")
	private Date submitTime;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getSolution() {
		return solution;
	}

	public void setSolution(String solution) {
		this.solution = solution;
	}

	public String getSolverId() {
		return solverId;
	}

	public void setSolverId(String solverId) {
		this.solverId = solverId;
	}

	public String getSolverName() {
		return solverName;
	}

	public void setSolverName(String solverName) {
		this.solverName = solverName;
	}

	public Date getSolveTime() {
		return solveTime;
	}

	public void setSolveTime(Date solveTime) {
		this.solveTime = solveTime;
	}

	public String getTriggerRecordId() {
		return triggerRecordId;
	}

	public void setTriggerRecordId(String triggerRecordId) {
		this.triggerRecordId = triggerRecordId;
	}

	public String getTriggerSendId() {
		return triggerSendId;
	}

	public void setTriggerSendId(String triggerSendId) {
		this.triggerSendId = triggerSendId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public String getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}

	public Boolean getSubmitted() {
		return submitted;
	}

	public void setSubmitted(Boolean submitted) {
		this.submitted = submitted;
	}

	public Date getSubmitTime() {
		return submitTime;
	}

	public void setSubmitTime(Date submitTime) {
		this.submitTime = submitTime;
	}

}
