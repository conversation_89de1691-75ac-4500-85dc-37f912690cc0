package com.github.cret.web.oee.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 反馈相关配置类，用于存储和管理反馈处理相关的URL配置
 */
@Component
@ConfigurationProperties(prefix = "feedback")
public class FeedbackConfig {

	// 反馈处理基础URL
	private String handleBaseUrl = "http://localhost:3333/feedback/handle";

	// 仪表板基础URL
	private String dashboardBaseUrl = "http://************:3000/dashboard";

	// 默认仪表板路径
	private String defaultDashboardPath = "SMT1-1";

	public String getHandleBaseUrl() {
		return handleBaseUrl;
	}

	public void setHandleBaseUrl(String handleBaseUrl) {
		this.handleBaseUrl = handleBaseUrl;
	}

	public String getDashboardBaseUrl() {
		return dashboardBaseUrl;
	}

	public void setDashboardBaseUrl(String dashboardBaseUrl) {
		this.dashboardBaseUrl = dashboardBaseUrl;
	}

	public String getDefaultDashboardPath() {
		return defaultDashboardPath;
	}

	public void setDefaultDashboardPath(String defaultDashboardPath) {
		this.defaultDashboardPath = defaultDashboardPath;
	}

	/**
	 * 构建完整的反馈处理URL（默认为编辑模式）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @return 完整的反馈处理URL
	 */
	public String buildFeedbackHandleUrl(String triggerRecordId, String sendRecordId) {
		return buildFeedbackHandleUrl("edit", triggerRecordId, sendRecordId);
	}

	/**
	 * 构建完整的反馈处理URL（支持不同操作类型）
	 * @param action 操作类型（edit-编辑/填写解决方案, view-查看解决方案）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @return 完整的反馈处理URL
	 */
	public String buildFeedbackHandleUrl(String action, String triggerRecordId, String sendRecordId) {
		return handleBaseUrl + "/" + action + "/" + triggerRecordId + "/" + sendRecordId;
	}

	/**
	 * 构建反馈编辑URL（用于填写解决方案）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @return 编辑URL
	 */
	public String buildFeedbackEditUrl(String triggerRecordId, String sendRecordId) {
		return buildFeedbackHandleUrl("edit", triggerRecordId, sendRecordId);
	}

	/**
	 * 构建反馈查看URL（用于查看解决方案）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @return 查看URL
	 */
	public String buildFeedbackViewUrl(String triggerRecordId, String sendRecordId) {
		return buildFeedbackHandleUrl("view", triggerRecordId, sendRecordId);
	}

	/**
	 * 构建反馈关闭URL（用于关闭异常）
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @return 关闭异常URL
	 */
	public String buildFeedbackCloseUrl(String triggerRecordId, String sendRecordId) {
		return buildFeedbackHandleUrl("close", triggerRecordId, sendRecordId);
	}

	/**
	 * 构建完整的仪表板URL
	 * @param dashboardPath 仪表板路径，如果为null则使用默认路径
	 * @return 完整的仪表板URL
	 */
	public String buildDashboardUrl(String dashboardPath) {
		String path = dashboardPath != null ? dashboardPath : defaultDashboardPath;
		return dashboardBaseUrl + "/" + path;
	}

	/**
	 * 构建默认仪表板URL
	 * @return 默认仪表板URL
	 */
	public String buildDefaultDashboardUrl() {
		return buildDashboardUrl(null);
	}

}
